# Xcode
#
# gitignore contributors: remember to keep this file compatible with the
# official gitignore recommendations for Xcode projects
# https://help.github.com/articles/ignoring-files/#ignoring-files-in-xcode

## User-specific files
*.xcuserdatad
*.xcworkspace
*.ds_store
*.xccheckout
*.moved-aside
*.xcuserstate
*.xcscmblueprint

## Build generated
build/
DerivedData/

# Swift Package Manager
#
# Add this line if you want to avoid checking in source code from Swift Package Manager dependencies.
# *.swiftpm
Package.resolved
Packages/

# Carthage
Carthage/Build
Carthage/Checkouts

# Pods
Pods/

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Environment files
.env*

.build/
.kiro/